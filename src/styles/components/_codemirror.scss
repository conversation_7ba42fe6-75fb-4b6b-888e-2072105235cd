@import "src/styles/colors";
@import "src/styles/typography";

.code-editor {
  user-select: text !important; // without this, codemirror won't work in WKWebView on M1!!!
  &.full {
    height: 100%;
    .cm-editor {
      height: 100%;
    }
  }

  .cm-editor {
    &.cm-focused {
      outline: none;
    }
    .cm-scroller {
      .cm-gutters {
        @include a-color($color-disabled);
        @include a-bk-color($color-default-outline);
        font-family: $font-family-mono;
        .cm-gutter {
          .cm-gutterElement {
            &.cm-activeLineGutter {
              @include a-color($color-default);
              @include a-bk-color($color-default-outline);
            }
          }
        }
      }
      .cm-content {
        font-family: $font-family-mono;
      }
    }
    .cm-selectionLayer {
      .cm-selectionBackground {
        @include a-bk-color($color-primary-slighter);
      }
    }
  }
}

/*

Typical Codemirror structure with CSS classes:

<div class="cm-editor [cm-focused] [generated classes]">
  <div class="cm-scroller">
    <div class="cm-gutters">
      <div class="cm-gutter [...]">
        <!-- One gutter element for each line -->
        <div class="cm-gutterElement">...</div>
      </div>
    </div>
    <div class="cm-content" contenteditable="true">
      <!-- The actual document content -->
      <div class="cm-line">Content goes here</div>
      <div class="cm-line">...</div>
    </div>
    <div class="cm-selectionLayer">
      <!-- Positioned rectangles to draw the selection -->
      <div class="cm-selectionBackground"></div>
    </div>
    <div class="cm-cursorLayer">
      <!-- Positioned elements to draw cursors -->
      <div class="cm-cursor"></div>
    </div>
  </div>
</div>

 */
