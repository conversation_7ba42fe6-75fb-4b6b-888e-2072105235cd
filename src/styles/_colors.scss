// Forward all variables and mixins to make them available when this file is used
@forward "sass:color";
@use "sass:color";
@use "sass:map";

$color-default-light: #000000;
$color-default-outline-light: #ffffff;

$color-primary-light: #7f76ce;
$color-primary-outline-light: $color-default-outline-light;
$color-primary-slight-light: #e1dff5;
$color-primary-slighter-light: #ebebf9;
$color-primary-slightest-light: #f6f6fa;

$color-positive-light: #32b47d;
$color-negative-light: #ff5858;
$color-subtle-light: #888888;
$color-disabled-light: #9e9e9e;

// STRUCTURAL

$color-struct-none-light: $color-default-outline-light;
$color-struct-section-light: #f1f1f1;
$color-struct-sub-section-light: #efefef;

$color-struct-border-light: #e9e9e9;
$color-struct-border-highlight-light: #dedede;

$color-struct-input-light: #e9e9e9;
$color-struct-input-highlight-light: #dcdbdb;

/*

Structural colours:
===================

These are colours that create structure and are used for
differentiating between areas, blocks and inputs so that
the user is clear on what is important and what is not.

$color-struct-none
$color-struct-section
$color-struct-sub-section

$color-struct-border
$color-struct-border-highlight

$color-struct-input
$color-struct-input-highlight

*/

// dark scheme
$color-default-dark: #e6e6e6;
$color-default-outline-dark: #000000;

$color-primary-dark: #7f76ce;
$color-primary-outline-dark: $color-default-outline-dark;
$color-primary-slight-dark: #7066bf;
$color-primary-slighter-dark: #48409a;
$color-primary-slightest-dark: #110f2f;

$color-positive-dark: #4cd599;
$color-negative-dark: #ff7676;
$color-subtle-dark: #a1a1a1;
$color-disabled-dark: #818181;

// STRUCTURAL

$color-struct-none-dark: $color-default-outline-dark;
$color-struct-section-dark: #101010;
$color-struct-sub-section-dark: #222222;

$color-struct-border-dark: #474747;
$color-struct-border-highlight-dark: #525252;

$color-struct-input-dark: #474747;
$color-struct-input-highlight-dark: #565656;

// light/dark pairs
$color-default: (
  light: $color-default-light,
  dark: $color-default-dark,
);
$color-default-outline: (
  light: $color-default-outline-light,
  dark: $color-default-outline-dark,
);

$color-primary: (
  light: $color-primary-light,
  dark: $color-primary-dark,
);
$color-primary-outline: (
  light: $color-primary-outline-light,
  dark: $color-primary-outline-dark,
);
$color-primary-slight: (
  light: $color-primary-slight-light,
  dark: $color-primary-slight-dark,
);
$color-primary-slighter: (
  light: $color-primary-slighter-light,
  dark: $color-primary-slighter-dark,
);
$color-primary-slightest: (
  light: $color-primary-slightest-light,
  dark: $color-primary-slightest-dark,
);

$color-positive: (
  light: $color-positive-light,
  dark: $color-positive-dark,
);
$color-negative: (
  light: $color-negative-light,
  dark: $color-negative-dark,
);
$color-subtle: (
  light: $color-subtle-light,
  dark: $color-subtle-dark,
);
$color-disabled: (
  light: $color-disabled-light,
  dark: $color-disabled-dark,
);
// STRUCTURAL pairs
$color-struct-none: (
  light: $color-struct-none-light,
  dark: $color-struct-none-dark,
);
$color-struct-section: (
  light: $color-struct-section-light,
  dark: $color-struct-section-dark,
);
$color-struct-sub-section: (
  light: $color-struct-sub-section-light,
  dark: $color-struct-sub-section-dark,
);
$color-struct-border: (
  light: $color-struct-border-light,
  dark: $color-struct-border-dark,
);
$color-struct-border-highlight: (
  light: $color-struct-border-highlight-light,
  dark: $color-struct-border-highlight-dark,
);
$color-struct-input: (
  light: $color-struct-input-light,
  dark: $color-struct-input-dark,
);
$color-struct-input-highlight: (
  light: $color-struct-input-highlight-light,
  dark: $color-struct-input-highlight-dark,
);

// material
$mat-primary: (
  50: color.adjust($color-primary-light, $lightness: 50%),
  100: color.adjust($color-primary-light, $lightness: 40%),
  200: color.adjust($color-primary-light, $lightness: 30%),
  300: color.adjust($color-primary-light, $lightness: 20%),
  400: color.adjust($color-primary-light, $lightness: 10%),
  500: $color-primary-light,
  600: color.adjust($color-primary-light, $lightness: -10%),
  700: color.adjust($color-primary-light, $lightness: -20%),
  800: color.adjust($color-primary-light, $lightness: -30%),
  900: color.adjust($color-primary-light, $lightness: -40%),
  A100: #ff80ab,
  A200: #ff4081,
  A400: #f50057,
  A700: #c51162,
  contrast: (50: $color-default-light,
    100: $color-default-light,
    200: $color-default-light,
    300: $color-default-light,
    400: $color-default-light,
    500: $color-default-outline-light,
    600: $color-default-outline-light,
    700: $color-default-outline-light,
    800: $color-default-outline-light,
    900: $color-default-outline-light,
    A100: $color-default-light,
    A200: $color-default-outline-light,
    A400: $color-default-outline-light,
    A700: $color-default-outline-light,
  ),
);
$mat-negative: (
  50: color.adjust($color-negative-light, $lightness: 50%),
  100: color.adjust($color-negative-light, $lightness: 40%),
  200: color.adjust($color-negative-light, $lightness: 30%),
  300: color.adjust($color-negative-light, $lightness: 20%),
  400: color.adjust($color-negative-light, $lightness: 10%),
  500: $color-negative-light,
  600: color.adjust($color-negative-light, $lightness: -10%),
  700: color.adjust($color-negative-light, $lightness: -20%),
  800: color.adjust($color-negative-light, $lightness: -30%),
  900: color.adjust($color-negative-light, $lightness: -40%),
  A100: #ff80ab,
  A200: #ff4081,
  A400: #f50057,
  A700: #c51162,
  contrast: (50: $color-default-light,
    100: $color-default-light,
    200: $color-default-light,
    300: $color-default-light,
    400: $color-default-light,
    500: $color-default-outline-light,
    600: $color-default-outline-light,
    700: $color-default-outline-light,
    800: $color-default-outline-light,
    900: $color-default-outline-light,
    A100: $color-default-light,
    A200: $color-default-outline-light,
    A400: $color-default-outline-light,
    A700: $color-default-outline-light,
  ),
);

$mat-primary-dark: (
  50: color.adjust($color-primary-dark, $lightness: 50%),
  100: color.adjust($color-primary-dark, $lightness: 40%),
  200: color.adjust($color-primary-dark, $lightness: 30%),
  300: color.adjust($color-primary-dark, $lightness: 20%),
  400: color.adjust($color-primary-dark, $lightness: 10%),
  500: $color-primary-dark,
  600: color.adjust($color-primary-dark, $lightness: -10%),
  700: color.adjust($color-primary-dark, $lightness: -20%),
  800: color.adjust($color-primary-dark, $lightness: -30%),
  900: color.adjust($color-primary-dark, $lightness: -40%),
  A100: #ff80ab,
  A200: #ff4081,
  A400: #f50057,
  A700: #c51162,
  contrast: (50: $color-default-dark,
    100: $color-default-dark,
    200: $color-default-dark,
    300: $color-default-dark,
    400: $color-default-dark,
    500: $color-default-outline-dark,
    600: $color-default-outline-dark,
    700: $color-default-outline-dark,
    800: $color-default-outline-dark,
    900: $color-default-outline-dark,
    A100: $color-default-dark,
    A200: $color-default-outline-dark,
    A400: $color-default-outline-dark,
    A700: $color-default-outline-dark,
  ),
);
$mat-negative-dark: (
  50: color.adjust($color-negative-dark, $lightness: 50%),
  100: color.adjust($color-negative-dark, $lightness: 40%),
  200: color.adjust($color-negative-dark, $lightness: 30%),
  300: color.adjust($color-negative-dark, $lightness: 20%),
  400: color.adjust($color-negative-dark, $lightness: 10%),
  500: $color-negative-dark,
  600: color.adjust($color-negative-dark, $lightness: -10%),
  700: color.adjust($color-negative-dark, $lightness: -20%),
  800: color.adjust($color-negative-dark, $lightness: -30%),
  900: color.adjust($color-negative-dark, $lightness: -40%),
  A100: #ff80ab,
  A200: #ff4081,
  A400: #f50057,
  A700: #c51162,
  contrast: (50: $color-default-dark,
    100: $color-default-dark,
    200: $color-default-dark,
    300: $color-default-dark,
    400: $color-default-dark,
    500: $color-default-outline-dark,
    600: $color-default-outline-dark,
    700: $color-default-outline-dark,
    800: $color-default-outline-dark,
    900: $color-default-outline-dark,
    A100: $color-default-dark,
    A200: $color-default-outline-dark,
    A400: $color-default-outline-dark,
    A700: $color-default-outline-dark,
  ),
);

@mixin in-dark() {
  @media only screen and (prefers-color-scheme: dark) {
    @content;
  }
}

@mixin a-color($color-map) {
  color: map.get($color-map, light);

  @include in-dark {
    color: map.get($color-map, dark);
  }
}

@mixin a-bk-color($color-map) {
  background-color: map.get($color-map, light);

  @include in-dark {
    background-color: map.get($color-map, dark);
  }
}