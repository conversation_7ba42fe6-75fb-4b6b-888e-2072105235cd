@use "./colors" as *;
@use "./typography" as *;
@use "sass:color";

input {
  font-family: $font-family-default;
  font-size: $font-size-default;
  accent-color: $color-primary-light;
  @include in-dark {
    accent-color: $color-primary-dark;
  }
  border: none;
  outline: 0;
  @include a-color($color-default);
  @include a-bk-color($color-default-outline);
  &::placeholder {
    font-weight: normal;
    @include in-dark {
      color: color.adjust($color-primary-slighter-light, $alpha: -0.5);
    }
  }

  &.primary {
    @include a-bk-color($color-primary-slight);
    &:hover {
      @include a-bk-color($color-primary-slightest);
    }
    &:focus {
      @include a-bk-color($color-primary-slightest);
    }
  }

  &.field {
    width: 100%;
    font-family: $font-family-default;
    padding: 0.75rem;
    border: solid 1px $color-struct-border-light;
    @include in-dark {
      border-color: $color-struct-border-dark;
    }
    border-radius: 0.5rem;

    &[readonly] {
      background-color: transparent;
      border: none;
    }
  }
}
