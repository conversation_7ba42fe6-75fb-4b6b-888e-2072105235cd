@use "../colors" as *;
@use "../typography" as *;

mat-accordion.mat-accordion {
  &.flat {
    mat-expansion-panel.mat-expansion-panel {
      box-shadow: none;
      border-radius: 0.5rem;

      mat-expansion-panel-header {
        border-radius: 0;
        @include a-bk-color($color-primary-outline);
        &:hover,
        &:hover:not([aria-disabled="true"]),
        &.cdk-program-focused:not([aria-disabled="true"]) {
          background-color: darken($color-primary-outline-light, 5%);
          @include in-dark {
            background-color: lighten($color-primary-outline-dark, 8%);
          }
        }
      }

      .mat-expansion-panel-content {
        .mat-expansion-panel-body {
          @include a-bk-color($color-primary-slightest);
          padding: 2rem;
        }
      }

      &.mat-expanded {
        border: solid 0.1rem $color-struct-border-light;
        @include in-dark {
          border-color: $color-struct-border-dark;
        }
        border-radius: 0.5rem;
        mat-expansion-panel-header {
          @include a-bk-color($color-primary-slightest);
          border-radius: 0;
          &:hover {
            background-color: lighten($color-primary-slightest-light, 1%);
            @include in-dark {
              background-color: darken($color-primary-slightest-dark, 5%);
            }
          }
        }
      }
    }
  }
}
