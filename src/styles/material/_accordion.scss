@use "../colors" as *;
@use "../typography" as *;
@use "sass:color";

mat-accordion.mat-accordion {
  &.flat {
    mat-expansion-panel.mat-expansion-panel {
      box-shadow: none;
      border-radius: 0.5rem;

      mat-expansion-panel-header {
        border-radius: 0;
        @include a-bk-color($color-primary-outline);

        &:hover,
        &:hover:not([aria-disabled="true"]),
        &.cdk-program-focused:not([aria-disabled="true"]) {
          background-color: color.adjust($color-primary-outline-light, $lightness: -5%);

          @include in-dark {
            background-color: color.adjust($color-primary-outline-dark, $lightness: 8%);
          }
        }
      }

      .mat-expansion-panel-content {
        .mat-expansion-panel-body {
          @include a-bk-color($color-primary-slightest);
          padding: 2rem;
        }
      }

      &.mat-expanded {
        border: solid 0.1rem $color-struct-border-light;

        @include in-dark {
          border-color: $color-struct-border-dark;
        }

        border-radius: 0.5rem;

        mat-expansion-panel-header {
          @include a-bk-color($color-primary-slightest);
          border-radius: 0;

          &:hover {
            background-color: color.adjust($color-primary-slightest-light, $lightness: 1%);

            @include in-dark {
              background-color: color.adjust($color-primary-slightest-dark, $lightness: -5%);
            }
          }
        }
      }
    }
  }
}