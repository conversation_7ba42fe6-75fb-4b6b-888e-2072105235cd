@import "../colors";

mat-tab-group.mat-mdc-tab-group {
  &.clean {
    height: 100%;
    max-height: 100%;
    mat-tab-header.mat-mdc-tab-header {
      height: 4rem;
      border-bottom: none;
      @include a-bk-color($color-primary-slightest);
      .mat-mdc-tab-list {
        height: 4rem;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        @include a-bk-color($color-primary-slightest);
        .mat-mdc-tab-labels {
          height: 3rem;
          border-bottom: solid 1px $color-struct-border-light;
          flex: unset;
          @include in-dark {
            border-bottom-color: $color-struct-border-dark;
          }
          .mdc-tab {
            height: 3rem;
            flex-grow: unset;
            padding-left: 1rem;
            padding-right: 1rem;
            min-width: unset;
            opacity: 1;
            @include a-color($color-default);
            border: solid 0.1rem transparent;
            border-radius: 0.5rem 0.5rem 0 0;
            &--active {
              font-weight: bold;
              @include a-color($color-default);
              @include a-bk-color($color-default-outline);
              transition: background-color 200ms;
              border-color: $color-struct-border-light;
              border-bottom-color: $color-struct-none-light;
              @include in-dark {
                border-color: $color-struct-border-dark;
                border-bottom-color: $color-struct-none-dark;
              }
            }
            &__text-label {
              @include a-color($color-default);
            }
            .mdc-tab-indicator,
            .mat-mdc-tab-ripple,
            .mdc-tab__ripple {
              display: none;
            }
          }
        }
      }
      .mat-mdc-tab-header-pagination {
        height: 4rem;
        align-self: end;
        border: none;
        border-bottom: solid 0.1rem $color-struct-border-light;
        min-width: 2.4rem;
        width: 2.4rem;
      }
    }
    .mat-mdc-tab-body-wrapper {
      height: calc(100% - 0.4rem);
      mat-tab-body.mat-mdc-tab-body {
        .mat-mdc-tab-body-content {
          overflow: auto;
        }
      }
    }
  }
}
