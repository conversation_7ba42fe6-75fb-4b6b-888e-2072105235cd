@import "../colors";
@import "../typography";

mat-select {
  &.fill {
    width: 100%;
    height: 100%;
    .mat-mdc-select-trigger {
      width: 100%;
      height: 100%;
      @include a-bk-color($color-primary-slight);
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      .mat-mdc-select-value {
        height: 100%;
        display: flex;
        align-items: center;
        padding-left: 1rem;
        font-weight: bold;
      }
      .mat-mdc-select-arrow-wrapper {
        height: 100%;
        min-width: $font-size-default;
      }
      &:hover {
        @include a-bk-color($color-primary-slightest);
      }
    }
  }

  &.inline {
    .mat-mdc-select-value {
      font-weight: normal;
      font-style: normal;
      text-decoration: underline;
    }
  }
}

.cdk-overlay-pane {
  min-width: 10rem;
  .mdc-menu-surface.mat-mdc-select-panel {
    max-height: 50rem;
    padding: 0;
    mat-option.mat-mdc-option {
      line-height: 2.2rem;
      height: 2.2em;
      min-height: 2.2rem;
      font-size: $font-size-default;
      &.detailed {
        line-height: 3rem;
        height: 4rem;
        /* TODO(mdc-migration): The following rule targets internal classes of option that may no longer apply for the MDC version. */
        .mat-option-text {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          & > *:first-child {
            display: block;
            line-height: 2rem;
            height: 2rem;
            @include a-color($color-default);
          }
          & > *:last-child {
            display: block;
            line-height: 1rem;
            height: 1rem;
            font-size: $font-size-small;
            @include a-color($color-subtle);
          }
        }
      }
    }
  }
}
