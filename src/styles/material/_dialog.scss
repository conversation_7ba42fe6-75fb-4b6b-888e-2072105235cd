@use "../colors" as *;
@use "../typography" as *;

mat-dialog-container.mat-mdc-dialog-container {
  padding: 0;
  div.mat-mdc-dialog-title {
    padding: 0;

    &::before {
      content: none;
    }
  }

  div.mat-mdc-dialog-content {
    margin: 0;
    padding: 3rem;
    text-align: center;
  }

  div.mat-mdc-dialog-actions {
    margin-bottom: 0;
    justify-content: center;
  }
}

$simple-dialog-title-height: 2.75rem;
$simple-dialog-footer-height: 5rem;

.simple-dialog {
  width: 50vw;
  max-width: 50vw;
  height: 75vh;
  max-height: 80vh;
  padding: 0;
  &.help {
    width: 50rem;
    max-width: 60rem;
    height: 75vh;
    max-height: 90vh;
  }
  h2 {
    height: $simple-dialog-title-height;
    @include a-color($color-primary);
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    font-size: $font-size-default;
    font-weight: bold;
    border-bottom: solid 1px $color-struct-border-light;
  }
  &--content {
    height: calc(100% - $simple-dialog-title-height - $simple-dialog-footer-height);
    &--scroll {
      height: 100%;
      overflow: auto;
      &--text {
        padding: 1rem;
      }
    }
  }
  &--footer {
    height: $simple-dialog-footer-height;
    border-top: solid 1px $color-struct-border-light;
    @include in-dark {
      border-top-color: $color-struct-border-dark;
    }
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
