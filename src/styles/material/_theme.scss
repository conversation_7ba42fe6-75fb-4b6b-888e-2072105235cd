@use "@angular/material" as mat;
@use "../colors" as *;
@use "../typography" as *;
@use "./buttons";
@use "./select";
@use "./tabs";
@use "./menu";
@use "./dialog";
@use "./accordion";
@use "./autocomplete";

@include mat.elevation-classes();
@include mat.app-background();

$hrst-typography: mat.m2-define-typography-config(
  $font-family: $font-family-default,
);
$hrst-web-primary-light: mat.m2-define-palette($mat-primary);
$hrst-web-accent-light: mat.m2-define-palette($mat-primary);
$hrst-web-warn-light: mat.m2-define-palette($mat-negative);
$hrst-web-theme: mat.m2-define-light-theme(
  (
    color: (
      primary: $hrst-web-primary-light,
      accent: $hrst-web-accent-light,
      warn: $hrst-web-warn-light,
    ),
    typography: $hrst-typography,
  )
);
@include mat.all-component-themes($hrst-web-theme);

@media (prefers-color-scheme: dark) {
  @include mat.all-component-colors(
    mat.m2-define-dark-theme(
      (
        color: (
          primary: mat.m2-define-palette($mat-primary-dark),
          accent: mat.m2-define-palette($mat-primary-dark),
          warn: mat.m2-define-palette($mat-negative-dark),
        ),
      )
    )
  );
}
