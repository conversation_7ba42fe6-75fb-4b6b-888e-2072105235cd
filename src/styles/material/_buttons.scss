@use "sass:math";
@import "../colors";
@import "../typography";

button.mat-mdc-button-base {
  .mdc-button__label {
    font-family: $font-family-default;
    font-size: $font-size-default;
    font-weight: bold;
  }
  &.small {
    height: 2.5rem;
    min-width: 2rem;
    .mdc-button__label {
      font-weight: normal;
    }
    .mat-mdc-button-touch-target {
      height: 2.5rem;
    }
    fa-icon {
      font-size: math.div($font-size-default + $font-size-small, 2);
    }
  }
}
