@use "./colors" as *;
@use "./fonts" as *;

$font-size-large: 1.6rem;
$font-size-default: 1.4rem;
$font-size-small: 1rem;

$font-family-default: "Quicksand", sans-serif;
$font-family-mono: "Courier Prime Code", "Cascadia";

html {
  // set a baseline rem where 1rem = 10px
  font-size: 10px;

  margin: 0;
  padding: 0;
}

body {
  font-family: $font-family-default;
  font-size: $font-size-default;

  padding: 0;
  margin: 0;

  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

  @include a-color($color-default);
  @include a-bk-color($color-default-outline);
}

a {
  @include a-color($color-primary);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: $font-size-default;
}

small {
  font-size: $font-size-small;
}

[tabindex="-1"] {
  outline: none;
}

pre {
  user-select: text;
  font-family: $font-family-mono;
}

code {
  user-select: text;
  font-family: $font-family-mono;
}

button {
  font-family: $font-family-default;
  font-size: $font-size-default;
  font-weight: bold;
  margin: 0;
  &.link {
    padding: 0;
    margin: 0;
    @include a-color($color-primary);
    text-decoration: underline;
    cursor: pointer;
    border: none;
    background-color: transparent;
    font-weight: normal;
  }
  &.icon {
    display: flex;
    gap: 0.25rem;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  &.borderless {
    padding: 0.1rem;
    margin: 0;
    @include a-color($color-default);
    text-decoration: none;
    cursor: pointer;
    border: none;
    background-color: transparent;
    font-weight: normal;
  }
}
