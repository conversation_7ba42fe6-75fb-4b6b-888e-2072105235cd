@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

$variable-new-title-height: 2.75rem;
$variable-new-footer-height: 5rem;

.variable-new {
  width: 35rem;

  h2 {
    height: $variable-new-title-height;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: $font-size-default;
    font-weight: bold;
  }

  &--view {
    height: calc(100% - $variable-new-title-height - $variable-new-footer-height);
    padding: 2rem 1rem;

    &--key {
      display: flex;
      gap: 1rem;
      align-items: center;

      label {
        width: 6rem;
        text-align: right;
      }
    }

    &--help {
      display: block;
      @include a-color($color-subtle);
      margin: 1rem 0 0 6rem;

      &.error {
        @include a-color($color-negative);
      }
    }
  }

  &--footer {
    height: $variable-new-footer-height;
    border-top: solid 1px $color-struct-border-light;

    @include in-dark {
      border-top-color: $color-struct-border-dark;
    }

    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}