@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

$variable-edit-environment-column-width: 12rem;

.variable {
  margin: 0.5rem;
  border-radius: 0.5rem;
  padding: 0.5rem;
  &--summary {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    &--data {
      cursor: pointer;
      flex-grow: 1;
      gap: 0.25rem;
      &--key {
        font-family: $font-family-mono;
      }
      &--value {
        @include a-color($color-subtle);
        font-size: $font-size-small;
        word-break: break-all;
      }
    }
    &--buttons {
      opacity: 0.5;
    }
  }
  &:hover {
    @include a-bk-color($color-primary-slightest);
    .variable--summary {
      &--buttons {
        opacity: 1;
      }
    }
  }
  &.expanded {
    @include a-bk-color($color-primary-slightest);
    .variable--summary {
      @include a-bk-color($color-primary-slighter);
      border-radius: 0.5rem;
      padding: 0.5rem;
      &--data {
        &--key {
          font-weight: bold;
        }
      }
    }
  }
  &--edit {
    @include a-bk-color($color-primary-slightest);
    padding-top: 1rem;
    &--header {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0 0 1rem 0;
      font-variant-caps: small-caps;

      &--name {
        width: $variable-edit-environment-column-width;
        padding-right: 1rem;
        text-align: right;
      }

      &--default-value {
        width: calc((100% - $variable-edit-environment-column-width) / 2);
      }

      &--current-value {
        width: calc((100% - $variable-edit-environment-column-width) / 2);
        padding-left: 0.75rem;
      }
    }

    &--environment {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0.25rem;
      &.active {
        font-style: italic;
      }

      &--name {
        width: $variable-edit-environment-column-width;
        padding-right: 1rem;
        text-align: right;
        font-weight: bold;
      }

      &--default-value {
        width: calc((100% - $variable-edit-environment-column-width) / 2);
      }

      &--current-value {
        width: calc((100% - $variable-edit-environment-column-width) / 2);
      }
    }

    &--buttons {
      padding: 1rem 1rem 0 1rem;
      display: flex;
      flex-direction: row;
      gap: 1rem;
      justify-content: center;
    }
  }
}
