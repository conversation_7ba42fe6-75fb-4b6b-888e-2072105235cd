@import "src/styles/colors";
@import "src/styles/typography";

$variables-view-title-height: 2.75rem;
$variables-view-section-title-height: 3.25rem;
$variables-view-table-header-height: 2.5rem;
$variables-view-buttons-width: 6rem;
$variables-view-footer-height: 5rem;

.variables {
  width: 75vw;
  max-width: 80rem;
  height: 75vh;
  max-height: 80vh;
  h2 {
    height: $variables-view-title-height;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: $font-size-default;
    font-weight: bold;
  }
  &--view {
    height: calc(100% - $variables-view-title-height - $variables-view-footer-height);
    &--title {
      height: $variables-view-section-title-height;
      padding: 0 1rem;
      @include a-bk-color($color-primary-slighter);
      @include a-color($color-primary);
      display: flex;
      align-items: center;
      justify-content: space-between;
      h3 {
        margin: 0;
        padding: 0;
        font-weight: normal;
        display: flex;
        gap: 0.5rem;
        em {
          font-style: normal;
          font-weight: bold;
        }
      }
    }
    &--variables {
      width: 100%;
      height: calc(100% - $variables-view-section-title-height);
      overflow: auto;
    }
    &--header {
      width: 100%;
      height: $variables-view-table-header-height;
      @include a-bk-color($color-primary-slightest);
      display: flex;
      align-items: center;
      padding: 0 1rem;
      & > * {
        width: calc((100% - $variables-view-buttons-width) / 3);
        font-weight: lighter;
        font-variant-caps: small-caps;
        &:last-child {
          width: $variables-view-buttons-width;
        }
      }
    }
    &--data {
      width: 100%;
      height: calc(
        100% - $variables-view-section-title-height - $variables-view-table-header-height
      );
      padding: 0.5rem;
      overflow: auto;
    }
  }
  &--footer {
    height: $variables-view-footer-height;
    border-top: solid 1px $color-struct-border-light;
    @include in-dark {
      border-top-color: $color-struct-border-dark;
    }
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
