@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

$environments-view-title-height: 2.75rem;
$environments-view-section-title-height: 3.25rem;
$environments-view-footer-height: 5rem;

.environments {
  width: 40rem;
  height: 50rem;
  min-width: 30rem;
  max-width: 80rem;
  min-height: 50vh;
  max-height: 80vh;
  h2 {
    height: $environments-view-title-height;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: $font-size-default;
    font-weight: bold;
  }
  &--view {
    height: calc(100% - $environments-view-title-height - $environments-view-footer-height);
    &--title {
      height: $environments-view-section-title-height;
      padding: 0 1rem;
      @include a-bk-color($color-primary-slighter);
      @include a-color($color-primary);
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
    &--data {
      width: 100%;
      height: calc(100% - $environments-view-section-title-height);
      padding: 0.5rem;
      overflow: auto;
    }
  }
  &--footer {
    height: $environments-view-footer-height;
    border-top: solid 1px $color-struct-border-light;
    @include in-dark {
      border-top-color: $color-struct-border-dark;
    }
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
