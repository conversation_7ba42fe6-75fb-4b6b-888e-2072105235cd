@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

$environments-view-buttons-width: 6rem;

.environment-row {
  width: 100%;
  display: flex;
  justify-content: space-between;

  &:hover {
    @include a-bk-color($color-primary-slightest);

    button {
      opacity: 1;
    }
  }

  &>* {
    width: calc(100% - $environments-view-buttons-width);

    &:last-child {
      width: $environments-view-buttons-width;
    }

    input {
      width: 100%;
      height: 100%;
      background-color: transparent;

      &::selection {
        @include a-bk-color($color-primary-slight);
      }
    }
  }

  button {
    opacity: 0.5;
  }

  &.new,
  &.edit {
    input:not([readonly]) {
      @include a-bk-color($color-struct-input);

      &::selection {
        @include a-color($color-primary-outline);
        @include a-bk-color($color-primary);
      }

      &:hover,
      &:focus {
        @include a-bk-color($color-struct-border-highlight);
      }
    }

    button {
      opacity: 1;
    }
  }
}