@use "src/styles/colors" as *;

$project-menu-height: 3.5rem;

.projects {
  height: 100%;

  &--menu {
    height: $project-menu-height;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.25rem;
    @include a-bk-color($color-primary-slightest);
    border-bottom: solid 1px $color-struct-border-light;

    @include in-dark {
      border-bottom-color: $color-struct-border-dark;
    }
  }

  &--tree {
    height: calc(100% - $project-menu-height);
    max-height: calc(100% - $project-menu-height);
    overflow: auto;
  }
}