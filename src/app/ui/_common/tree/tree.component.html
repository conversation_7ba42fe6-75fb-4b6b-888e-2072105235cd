@if (dataSource) {
  <div
    class="tree"
    cdkDropList
    [cdkDropListSortPredicate]="allowDrop"
    (cdkDropListDropped)="drop($event)"
  >
    @for (node of dataSource.treeNodes; track node) {
      <div
        class="tree-node"
        [ngClass]="{
          selected: dataSource.isSelected(node),
          extra: node.extraInfo != null,
        }"
        (click)="selectNode(node)"
        (dblclick)="toggleExpand(node)"
        (contextmenu)="showContextMenu($event, node)"
        cdkDrag
        [cdkDragData]="node"
        [cdkDragDisabled]="!delegate?.allowsDrag(node)"
      >
        <div class="tree-node--drag-placeholder" *cdkDragPlaceholder></div>
        <div class="tree-node--drag-preview" *cdkDragPreview>
          <fa-icon [icon]="node.icon"></fa-icon>
          <div>{{ node.label }}</div>
        </div>
        <div
          class="tree-node--menu-trigger"
          [matMenuTriggerFor]="treeMenu"
          [ngStyle]="{
            'top.px': menuPosition.top,
            'left.px': menuPosition.left,
          }"
        ></div>
        @for (item of [].constructor(node.level); track item; let i = $index) {
          <div class="tree-node--level"></div>
        }
        <div class="tree-node--expand" (click)="toggleExpand(node)">
          @if (dataSource?.isExpanded(node)) {
            <fa-icon [icon]="icons.nodeExpanded"></fa-icon>
          }
          @if (!dataSource.isLeaf(node) && !dataSource.isExpanded(node)) {
            <fa-icon [icon]="icons.nodeCollapsed"></fa-icon>
          }
        </div>
        <div class="tree-node--icon">
          <fa-icon [icon]="node.icon"></fa-icon>
        </div>
        @if (!dataSource.isRenaming(node)) {
          <div class="tree-node--name">
            <div class="tree-node--name--label" (dblclick)="renameNode(node)">
              {{ node.label }}
            </div>
            @if (node.extraInfo) {
              <div class="tree-node--name--extra">
                @for (info of node.extraInfo; track info) {
                  <div class="tree-node--name--extra--info" [ngClass]="info.style">
                    {{ info.info }}
                  </div>
                }
              </div>
            }
          </div>
        }
        @if (dataSource.allowsRename(node)) {
          <input
            #editField
            [id]="'projects-tree-node-' + node.id"
            type="text"
            [value]="node.label"
            [ngStyle]="{
              display: dataSource.isRenaming(node) ? 'block' : 'none',
            }"
            (keydown)="editFieldKeyDown($event, node, editField)"
            (blur)="commitEditing(node, editField)"
          />
        }
      </div>
    }
  </div>
}

<mat-menu #treeMenu="matMenu">
  <ng-template matMenuContent let-node="node" let-contextActions="contextActions">
    @for (action of contextActions; track action) {
      <div>
        @if (action.label != "-") {
          <button
            mat-menu-item
            [ngClass]="{
              negative: action.negative,
            }"
            (click)="action.action(node)"
            [disabled]="action.disabled"
          >
            <fa-icon [icon]="action.icon"></fa-icon>
            {{ action.label }}
          </button>
        }
        @if (action.label == "-") {
          <div>
            <hr class="separator" />
          </div>
        }
      </div>
    }
  </ng-template>
</mat-menu>
