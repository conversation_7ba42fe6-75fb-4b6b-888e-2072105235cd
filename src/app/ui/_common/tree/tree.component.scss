@use "src/styles/colors" as *;
@use "src/styles/typography" as *;
@use "sass:color";

$tree-node-height: 2.5rem;
$tree-node-element-width: 2rem;

.tree-node--drag-preview {
  padding: 1rem;
  display: flex;
  background-color: color.adjust($color-default-outline-light, $alpha: -0.33);
  @include in-dark {
    background-color: color.adjust($color-default-outline-dark, $alpha: -0.33);
  }
  border: solid 1px $color-struct-border-light;
  @include in-dark {
    border-color: $color-struct-border-dark;
  }

  fa-icon {
    margin-right: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: $font-size-default;
    @include a-color($color-primary);
  }
}

.tree {
  .tree-node {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    min-height: $tree-node-height;
    font-size: $font-size-default;
    white-space: nowrap;

    &:hover {
      @include a-bk-color($color-primary-slightest);
    }

    &.selected {
      @include a-bk-color($color-primary-slighter);
    }

    &.extra {
      padding: 0.33rem 0;
      .tree-node--icon {
        align-self: flex-start;
        padding-top: 0.25rem;
      }
    }

    &--drag-placeholder {
      border-bottom: dotted 0.25rem $color-primary-light;
      @include in-dark {
        border-color: $color-primary-dark;
      }
    }

    &--menu-trigger {
      width: 0;
      height: 0;
      position: absolute;
    }

    &--level {
      width: $tree-node-element-width;
      min-width: $tree-node-element-width;
      max-width: $tree-node-element-width;
      height: 100%;
    }

    &--expand {
      width: $tree-node-element-width;
      min-width: $tree-node-element-width;
      max-width: $tree-node-element-width;
      height: 100%;

      fa-icon {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: $font-size-small;
        @include a-color($color-subtle);
      }
    }

    &--icon {
      width: $tree-node-element-width;
      min-width: $tree-node-element-width;
      max-width: $tree-node-element-width;
      height: 100%;

      fa-icon {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: $font-size-default;
        @include a-color($color-primary);
      }
    }

    &--name {
      cursor: default;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      &--extra {
        display: flex;
        flex-direction: row;
        gap: 0.5rem;
      }
    }
  }
}
