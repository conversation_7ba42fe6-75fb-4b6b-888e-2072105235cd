@import "src/styles/colors";
@import "src/styles/typography";

.name-value {
  width: 100%;
  display: flex;
  input {
    font-family: $font-family-mono;
    width: 100%;
    border: none;
    padding: 0.5rem 0.5rem 0.25rem 0.5rem;
    box-shadow: none;
    border-radius: 0.5rem;
    outline: none;
    background-color: transparent;
    @include a-color($color-default);
    &:hover {
      @include a-bk-color($color-struct-input);
    }
    &:focus {
      @include a-bk-color($color-struct-input-highlight);
    }
  }
  &.disabled {
    input {
      @include a-color($color-disabled);
      text-decoration-line: line-through;
    }
  }
  &--name {
    width: 50%;
    input {
      text-align: right;
      font-weight: bold;
    }
  }
  &--menu {
    width: 2rem;
    button {
      width: 100%;
      height: 100%;
      border: none;
      box-shadow: none;
      border-radius: 0.5rem;
      margin: 0;
      background-color: transparent;
      @include a-color($color-default);
      &:hover {
        @include a-bk-color($color-struct-input-highlight);
      }
    }
  }
  &--value {
    width: 50%;
  }
}
