@import "src/styles/colors";
@import "src/styles/typography";

$type-height: 2rem;

.request-body-editor {
  height: 100%;
  max-height: 100%;
  padding: 0;
  display: flex;
  flex-direction: column;
  &--type {
    height: $type-height;
    max-height: $type-height;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: row;
    justify-content: center;
    border-bottom: solid 1px $color-struct-border-light;
    @include in-dark {
      border-bottom-color: $color-struct-border-dark;
    }
    li {
      list-style: none;
      padding: 0 0.5rem;
      button {
        font-size: $font-size-small;
        font-weight: normal;
        &.selected {
          font-weight: bold;
          text-decoration: none;
        }
      }
    }
  }
  &--editor {
    height: calc(100% - $type-height);
    min-height: calc(100% - $type-height);
    max-height: calc(100% - $type-height);
  }
}
