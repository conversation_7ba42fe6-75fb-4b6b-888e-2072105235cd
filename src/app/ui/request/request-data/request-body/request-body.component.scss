@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

$title-height: 3rem;

.request-body {
  height: 100%;
  &--title {
    @include a-color($color-subtle);
    @include a-bk-color($color-primary-slightest);
    font-weight: lighter;
    font-size: $font-size-small;
    line-height: $font-size-small;
    font-variant-caps: small-caps;
    padding: 0.5rem 0 0 1rem;
    margin-bottom: -0.5rem;
    position: relative;
    z-index: 2;
  }
  mat-tab-group {
    height: calc(100% - 1rem);
  }
}

.request-body--tab {
  display: flex;
  gap: 1rem;
  align-items: center;
}

fa-icon {
  @include a-color($color-primary);
}
