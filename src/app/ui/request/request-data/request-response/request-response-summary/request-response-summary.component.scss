@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

.response-summary {
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  @include a-bk-color($color-primary-slightest);
  border-bottom: solid 0.1rem $color-struct-border-light;

  @include in-dark {
    border-bottom-color: $color-struct-border-dark;
  }

  &--status {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    align-items: center;
    font-weight: bold;

    fa-icon {
      font-size: $font-size-small;
      @include a-color($color-positive);
    }

    &.negative {
      fa-icon {
        @include a-color($color-negative);
      }
    }
  }

  &--waiting {
    font-style: italic;
    @include a-color($color-disabled);
  }
}