@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

.request-response-headers {
  &--none {
    margin-top: 15%;
    text-align: center;
    @include a-color($color-disabled);

    fa-icon {
      @include a-color($color-primary);
    }
  }

  &--table {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
    margin-bottom: 2rem;

    thead {
      font-weight: bold;
      font-variant-caps: all-small-caps;
      @include a-color($color-subtle);

      td {
        padding: 0.25rem 1rem;

        &:first-child {
          text-align: right;
        }
      }
    }

    tbody {
      tr {
        td {
          border-top: solid 1px $color-struct-border-light;

          @include in-dark {
            border-top-color: $color-struct-border-dark;
          }

          user-select: text;
          font-family: $font-family-mono;
          vertical-align: top;
          padding: 0.5rem 1rem;

          &:first-child {
            width: 40%;
            text-align: right;
            font-weight: bold;
          }

          &:last-child {
            width: 60%;
            word-break: break-all;
          }
        }

        &:last-child {
          td {
            border-bottom: solid 1px $color-struct-border-light;

            @include in-dark {
              border-bottom-color: $color-struct-border-dark;
            }
          }
        }
      }
    }
  }
}