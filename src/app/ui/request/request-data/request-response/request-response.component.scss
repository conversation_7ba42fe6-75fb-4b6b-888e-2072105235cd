@import "src/styles/colors";
@import "src/styles/typography";

$summary-height: 4rem;
$footer-height: 6rem;

.request-response {
  width: 100%;
  height: 100%;
  kng-request-response-summary {
    height: $summary-height;
    max-height: $summary-height;
    display: block;
  }
  &--view {
    height: calc(100% - $summary-height - $footer-height);
    max-height: calc(100% - $summary-height - $footer-height);
    overflow: auto;
    padding: 0;
  }
  &--variables {
    height: $footer-height;
    border-top: solid 0.1rem $color-struct-border-light;
    @include in-dark {
      border-top-color: $color-struct-border-dark;
    }
    display: flex;
    flex-direction: column;
    &--expand {
      @include a-color($color-primary);
      @include a-bk-color($color-primary-slightest);
      button {
        width: 100%;
        border-radius: 0;
      }
    }
    &--summary {
      padding: 0 1rem;
      flex-grow: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  &.no-footer {
    .request-response--view {
      height: calc(100% - $summary-height);
      max-height: calc(100% - $summary-height);
    }
  }
}
