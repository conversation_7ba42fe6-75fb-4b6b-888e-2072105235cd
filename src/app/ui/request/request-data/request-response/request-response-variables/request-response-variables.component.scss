@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

$variables-header-height: 3rem;

.response-variables {
  height: 100%;
  display: flex;
  flex-direction: column;

  @include in-dark {
    border-top-color: $color-struct-border-dark;
  }

  &--collapse {
    height: $variables-header-height;
    @include a-color($color-primary);
    @include a-bk-color($color-primary-slightest);

    button {
      width: 100%;
      height: 100%;
      border-radius: 0;
    }
  }

  &--content {
    height: calc(100% - $variables-header-height);
    overflow: auto;
  }
}