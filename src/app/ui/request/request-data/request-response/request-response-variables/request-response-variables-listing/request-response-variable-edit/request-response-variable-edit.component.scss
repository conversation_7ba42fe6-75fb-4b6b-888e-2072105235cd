@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

.variable-edit {
  @include a-bk-color($color-primary-slightest);
  &--no-vars {
    display: flex;
    flex-direction: column;
    align-items: center;
    button {
      display: flex;
      gap: 0.5rem;
    }
  }
  &--values {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    input {
      &[readonly] {
        @include a-color($color-subtle);
        @include a-bk-color($color-primary-slightest);
      }
    }
    &--evaluate {
      padding: 0.75rem;
    }
    &--value {
      display: flex;
      align-items: baseline;
      padding: 0 0.75rem;
      gap: 0.5rem;
      input {
        flex-grow: 1;
        font-family: $font-family-mono;
      }
    }
  }
  &--validation {
    padding: 1rem;
    @include a-bk-color($color-primary-slight);
    display: flex;
    flex-direction: column;
    align-items: center;
    &--message {
      fa-icon {
        @include a-color($color-negative);
      }
    }
    button {
      margin: 0.5rem;
    }
  }
  &--buttons {
    padding: 1rem 1rem 0 1rem;
    display: flex;
    flex-direction: row;
    gap: 1rem;
    justify-content: center;
  }
}
