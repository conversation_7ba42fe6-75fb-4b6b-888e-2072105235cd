@import "src/styles/colors";
@import "src/styles/typography";

.variables-listing {
  padding: 0.5rem;
  &--accordion {
    &--header {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      &--var {
        white-space: nowrap;
      }
      &--value {
        @include a-color($color-subtle);
        font-size: $font-size-small;
        white-space: nowrap;
      }
    }
  }
  &--edit-var {
    display: flex;
  }
  &--new {
    margin-top: 0.5rem;
    display: flex;
    justify-content: center;
    button {
      fa-icon {
        font-size: $font-size-large;
      }
    }
  }
}
